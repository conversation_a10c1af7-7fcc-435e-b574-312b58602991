import cv2
import numpy as np
import json
import csv
from scipy.spatial.transform import Rotation as R
import os

def load_camera_parameters(json_path):
    """加载相机内参和外参"""
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    camera_info = data['cameras'][0]
    
    # 相机内参
    fx = camera_info['fx']
    fy = camera_info['fy']
    cx = camera_info['cx']
    cy = camera_info['cy']
    
    # 相机外参（初始姿态）
    rotation = camera_info['tf_vehicle_camera']['rotation']
    translation = camera_info['tf_vehicle_camera']['translation']
    
    # 创建相机内参矩阵
    K = np.array([[fx, 0, cx],
                  [0, fy, cy],
                  [0, 0, 1]])
    
    return K, rotation, translation

def detect_vanishing_point(image):
    """检测图像中的消失点"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 高斯滤波去噪
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Canny边缘检测
    edges = cv2.Canny(blurred, 50, 150, apertureSize=3)
    
    # 霍夫变换检测直线（在ROI区域）
    lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
    
    if lines is None:
        return None, edges
    
    # 提取前几条最显著的直线（用于计算消失点）
    significant_lines = []
    for line in lines[:min(20, len(lines))]:  # 取前20条线
        rho, theta = line[0]
        a = np.cos(theta)
        b = np.sin(theta)
        x0 = a * rho
        y0 = b * rho
        x1 = int(x0 + 1000 * (-b))
        y1 = int(y0 + 1000 * (a))
        x2 = int(x0 - 1000 * (-b))
        y2 = int(y0 - 1000 * (a))
        significant_lines.append(((x1, y1), (x2, y2)))
    
    # 计算消失点（使用前几条直线的交点）
    vanishing_point = None
    if len(significant_lines) >= 2:
        try:
            # 简单地计算前两条线的交点作为消失点
            line1 = significant_lines[0]
            line2 = significant_lines[1]
            
            point1_1, point1_2 = line1
            point2_1, point2_2 = line2
            
            # 将直线转换为一般式方程 ax + by + c = 0
            a1, b1 = point1_2[1] - point1_1[1], point1_1[0] - point1_2[0]
            c1 = a1 * point1_1[0] + b1 * point1_1[1]
            
            a2, b2 = point2_2[1] - point2_1[1], point2_1[0] - point2_2[0]
            c2 = a2 * point2_1[0] + b2 * point2_1[1]
            
            # 计算交点
            det = a1 * b2 - a2 * b1
            if abs(det) > 1e-10:
                x = (b2 * c1 - b1 * c2) / det
                y = (a1 * c2 - a2 * c1) / det
                vanishing_point = (int(x), int(y))
        except Exception as e:
            print(f"计算消失点时出错: {e}")
    
    return vanishing_point, edges

def calculate_camera_pose_from_vanishing_point(vanishing_point, K, initial_rotation, initial_translation):
    """从消失点计算相机姿态角变化"""
    if vanishing_point is None:
        return None
    
    # 消失点像素坐标
    vp_x, vp_y = vanishing_point
    
    # 将消失点转换为归一化图像坐标（相对于主点）
    norm_x = (vp_x - K[0, 2]) / K[0, 0]
    norm_y = (vp_y - K[1, 2]) / K[1, 1]
    
    # 假设消失点反映了相机的旋转，这里是一个简化的模型
    # 实际上需要更复杂的几何关系来反推完整的姿态角
    
    # 简化处理：使用消失点位置计算近似的旋转角度变化（基于图像平面的运动）
    roll = 0.0  # 滚转角
    pitch = -norm_y * 0.1  # 俯仰角，这里是一个简化比例关系
    yaw = norm_x * 0.1  # 偏航角
    
    return {
        'vanishing_point': vanishing_point,
        'roll': roll,
        'pitch': pitch,
        'yaw': yaw
    }

def main():
    """主处理函数"""
    json_path = "data/R9_Aw_CamS.json"
    video_path = "data/R9_Aw_CamS.mp4"
    
    # 加载相机参数
    K, initial_rotation, initial_translation = load_camera_parameters(json_path)
    print("相机内参矩阵:")
    print(K)
    print(f"初始旋转四元数: w={initial_rotation['w']}, x={initial_rotation['x']}, y={initial_rotation['y']}, z={initial_rotation['z']}")
    print(f"初始平移向量: x={initial_translation['x']}, y={initial_translation['y']}, z={initial_translation['z']}")
    
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        print("错误：无法打开视频文件")
        return
    
    # 获取视频信息
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    print(f"视频信息 - FPS: {fps}, 帧数: {frame_count}, 尺寸: {width}x{height}")
    
    # 创建输出目录
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 打开CSV文件写入结果
    csv_path = os.path.join(output_dir, "camera_pose_results.csv")
    with open(csv_path, 'w', newline='') as csvfile:
        fieldnames = ['frame_number', 'vanishing_point_x', 'vanishing_point_y', 
                     'roll', 'pitch', 'yaw']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        frame_num = 0
        while True:
            ret, frame = cap.read()
            
            if not ret:
                break
            
            # 检测消失点
            vanishing_point, edge_image = detect_vanishing_point(frame)
            
            # 计算相机姿态（简化处理）
            pose_result = calculate_camera_pose_from_vanishing_point(
                vanishing_point, K, initial_rotation, initial_translation)
            
            # 写入CSV结果
            if pose_result:
                writer.writerow({
                    'frame_number': frame_num,
                    'vanishing_point_x': pose_result['vanishing_point'][0],
                    'vanishing_point_y': pose_result['vanishing_point'][1],
                    'roll': pose_result['roll'],
                    'pitch': pose_result['pitch'],
                    'yaw': pose_result['yaw']
                })
            else:
                writer.writerow({
                    'frame_number': frame_num,
                    'vanishing_point_x': 0,
                    'vanishing_point_y': 0,
                    'roll': 0.0,
                    'pitch': 0.0,
                    'yaw': 0.0
                })
            
            # 保存可视化结果（可选）
            if vanishing_point:
                # 在原图上标记消失点和直线
                marked_frame = frame.copy()
                
                # 绘制用于计算消失点的前两条直线
                lines = cv2.HoughLines(edge_image, 1, np.pi/180, threshold=100)
                if lines is not None and len(lines) >= 2:
                    # 绘制前两条直线（在标记图像上）
                    for i, line in enumerate(lines[:2]):
                        rho, theta = line[0]
                        a = np.cos(theta)
                        b = np.sin(theta)
                        x0 = a * rho
                        y0 = b * rho
                        x1 = int(x0 + 1000 * (-b))
                        y1 = int(y0 + 1000 * (a))
                        x2 = int(x0 - 1000 * (-b))
                        y2 = int(y0 - 1000 * (a))
                        
                        # 绘制直线
                        cv2.line(marked_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    
                    # 在原图上标记消失点
                    cv2.circle(marked_frame, vanishing_point, 5, (0, 0, 255), -1)
                
                # 只保存标记后的图像（每帧一张）
                marked_path = os.path.join(output_dir, f"frame_{frame_num:04d}_marked.png")
                cv2.imwrite(marked_path, marked_frame)
            
            frame_num += 1
            
            if frame_num % 50 == 0:
                print(f"已处理 {frame_num} 帧")
    
    cap.release()
    print("处理完成！结果保存在:", csv_path)

if __name__ == "__main__":
    main()
