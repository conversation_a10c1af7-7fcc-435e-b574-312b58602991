import cv2
import numpy as np
import json
import csv
from scipy.spatial.transform import Rotation as R
import os
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
from scipy.optimize import least_squares
from collections import deque
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_camera_parameters(json_path):
    """加载相机内参和外参"""
    with open(json_path, 'r') as f:
        data = json.load(f)
    
    camera_info = data['cameras'][0]
    
    # 相机内参
    fx = camera_info['fx']
    fy = camera_info['fy']
    cx = camera_info['cx']
    cy = camera_info['cy']
    
    # 相机外参（初始姿态）
    rotation = camera_info['tf_vehicle_camera']['rotation']
    translation = camera_info['tf_vehicle_camera']['translation']
    
    # 创建相机内参矩阵
    K = np.array([[fx, 0, cx],
                  [0, fy, cy],
                  [0, 0, 1]])
    
    return K, rotation, translation

class RobustVanishingPointDetector:
    """Robust vanishing point detector with outlier rejection and confidence estimation"""

    def __init__(self, image_width, image_height):
        self.image_width = image_width
        self.image_height = image_height
        self.min_line_length = min(image_width, image_height) * 0.1
        self.max_line_gap = 20

    def detect_lines_with_quality(self, image):
        """Detect lines with quality assessment and outlier rejection"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Enhanced preprocessing
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Adaptive Canny edge detection
        median_val = np.median(blurred)
        lower = int(max(0, 0.7 * median_val))
        upper = int(min(255, 1.3 * median_val))
        edges = cv2.Canny(blurred, lower, upper, apertureSize=3)

        # Use HoughLinesP for better line segment detection
        lines_p = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50,
                                  minLineLength=self.min_line_length,
                                  maxLineGap=self.max_line_gap)

        if lines_p is None:
            return [], edges

        # Convert to line parameters and filter
        quality_lines = []
        for line in lines_p:
            x1, y1, x2, y2 = line[0]

            # Calculate line length and angle
            length = np.sqrt((x2-x1)**2 + (y2-y1)**2)
            if length < self.min_line_length:
                continue

            # Calculate line parameters (rho, theta)
            if x2 == x1:  # Vertical line
                theta = np.pi/2
                rho = x1
            else:
                slope = (y2-y1)/(x2-x1)
                theta = np.arctan(-1/slope) if slope != 0 else 0
                rho = (x1*np.cos(theta) + y1*np.sin(theta))

            # Normalize theta to [0, pi)
            if theta < 0:
                theta += np.pi
                rho = -rho

            # Quality score based on length and edge strength
            quality = length / max(self.image_width, self.image_height)

            quality_lines.append({
                'rho': rho,
                'theta': theta,
                'length': length,
                'quality': quality,
                'endpoints': ((x1, y1), (x2, y2))
            })

        # Sort by quality and return top lines
        quality_lines.sort(key=lambda x: x['quality'], reverse=True)
        return quality_lines[:30], edges  # Keep top 30 lines

    def cluster_lines(self, lines):
        """Cluster similar lines to reduce noise"""
        if len(lines) < 2:
            return lines

        # Create feature matrix (rho, theta)
        features = np.array([[line['rho'], line['theta']] for line in lines])

        # Normalize features
        rho_std = np.std(features[:, 0]) if np.std(features[:, 0]) > 0 else 1
        theta_std = np.std(features[:, 1]) if np.std(features[:, 1]) > 0 else 1

        normalized_features = features.copy()
        normalized_features[:, 0] /= rho_std
        normalized_features[:, 1] /= theta_std

        # DBSCAN clustering
        try:
            clustering = DBSCAN(eps=0.3, min_samples=2).fit(normalized_features)
            labels = clustering.labels_

            # Get representative line from each cluster
            clustered_lines = []
            unique_labels = set(labels)

            for label in unique_labels:
                if label == -1:  # Noise points
                    continue

                cluster_lines = [lines[i] for i in range(len(lines)) if labels[i] == label]
                if cluster_lines:
                    # Choose the highest quality line from cluster
                    best_line = max(cluster_lines, key=lambda x: x['quality'])
                    clustered_lines.append(best_line)

            # Add noise points if they have high quality
            noise_lines = [lines[i] for i in range(len(lines)) if labels[i] == -1]
            high_quality_noise = [line for line in noise_lines if line['quality'] > 0.1]
            clustered_lines.extend(high_quality_noise)

            return clustered_lines if clustered_lines else lines[:10]

        except Exception as e:
            logging.warning(f"Line clustering failed: {e}")
            return lines[:10]

    def compute_vanishing_point_ransac(self, lines, max_iterations=1000, threshold=5.0):
        """Compute vanishing point using RANSAC for robustness"""
        if len(lines) < 2:
            return None, 0.0

        best_vp = None
        best_inliers = 0
        best_confidence = 0.0

        for _ in range(max_iterations):
            # Randomly sample two lines
            if len(lines) < 2:
                break

            sample_indices = np.random.choice(len(lines), 2, replace=False)
            line1, line2 = lines[sample_indices[0]], lines[sample_indices[1]]

            # Compute intersection
            vp = self.line_intersection(line1, line2)
            if vp is None:
                continue

            # Check if vanishing point is reasonable (not too far from image)
            max_coord = max(self.image_width, self.image_height) * 3
            if abs(vp[0]) > max_coord or abs(vp[1]) > max_coord:
                continue

            # Count inliers
            inliers = 0
            total_quality = 0

            for line in lines:
                distance = self.point_to_line_distance(vp, line)
                if distance < threshold:
                    inliers += 1
                    total_quality += line['quality']

            # Confidence based on inliers and quality
            confidence = (inliers / len(lines)) * (total_quality / len(lines))

            if inliers > best_inliers or (inliers == best_inliers and confidence > best_confidence):
                best_vp = vp
                best_inliers = inliers
                best_confidence = confidence

        return best_vp, best_confidence

    def line_intersection(self, line1, line2):
        """Compute intersection of two lines"""
        try:
            rho1, theta1 = line1['rho'], line1['theta']
            rho2, theta2 = line2['rho'], line2['theta']

            # Convert to ax + by = c form
            a1, b1 = np.cos(theta1), np.sin(theta1)
            a2, b2 = np.cos(theta2), np.sin(theta2)
            c1, c2 = rho1, rho2

            # Solve system of equations
            det = a1 * b2 - a2 * b1
            if abs(det) < 1e-10:  # Lines are parallel
                return None

            x = (c1 * b2 - c2 * b1) / det
            y = (a1 * c2 - a2 * c1) / det

            return (x, y)

        except Exception:
            return None

    def point_to_line_distance(self, point, line):
        """Compute distance from point to line"""
        try:
            px, py = point
            rho, theta = line['rho'], line['theta']

            # Distance = |ax + by - c| / sqrt(a^2 + b^2)
            a, b = np.cos(theta), np.sin(theta)
            distance = abs(a * px + b * py - rho)

            return distance

        except Exception:
            return float('inf')

    def detect_vanishing_point(self, image):
        """Main vanishing point detection function"""
        # Detect and filter lines
        lines, edges = self.detect_lines_with_quality(image)

        if len(lines) < 2:
            return None, edges, 0.0, []

        # Cluster similar lines
        clustered_lines = self.cluster_lines(lines)

        if len(clustered_lines) < 2:
            return None, edges, 0.0, clustered_lines

        # Compute vanishing point using RANSAC
        vanishing_point, confidence = self.compute_vanishing_point_ransac(clustered_lines)

        return vanishing_point, edges, confidence, clustered_lines

class RobustPoseEstimator:
    """Robust camera pose estimator with temporal smoothing"""

    def __init__(self, K, initial_rotation, smoothing_window=5):
        self.K = K
        self.initial_rotation = initial_rotation
        self.smoothing_window = smoothing_window
        self.pose_history = deque(maxlen=smoothing_window)
        self.confidence_history = deque(maxlen=smoothing_window)

    def vanishing_point_to_pose(self, vanishing_point, confidence=1.0):
        """Convert vanishing point to camera pose with proper geometric model"""
        if vanishing_point is None:
            return None

        vp_x, vp_y = vanishing_point

        # Convert to normalized image coordinates
        norm_x = (vp_x - self.K[0, 2]) / self.K[0, 0]
        norm_y = (vp_y - self.K[1, 2]) / self.K[1, 1]

        # Improved geometric model for pose estimation
        # Assuming the vanishing point corresponds to the principal direction

        # Calculate rotation angles using perspective geometry
        # The vanishing point represents the direction of parallel lines in 3D

        # Convert normalized coordinates to 3D direction vector
        direction_3d = np.array([norm_x, norm_y, 1.0])
        direction_3d = direction_3d / np.linalg.norm(direction_3d)

        # Calculate rotation angles
        # Yaw (rotation around Z-axis)
        yaw = np.arctan2(direction_3d[0], direction_3d[2])

        # Pitch (rotation around X-axis)
        pitch = -np.arcsin(direction_3d[1])

        # Roll estimation (limited information from single vanishing point)
        # Use a more conservative approach
        roll = 0.0  # Single vanishing point doesn't provide roll information

        # Apply scaling factors based on camera characteristics
        # These factors should be calibrated based on the specific setup
        yaw_scale = 0.5  # Reduced from 0.1 for more realistic scaling
        pitch_scale = 0.5

        pose = {
            'vanishing_point': vanishing_point,
            'roll': roll,
            'pitch': pitch * pitch_scale,
            'yaw': yaw * yaw_scale,
            'confidence': confidence,
            'raw_angles': {'roll': roll, 'pitch': pitch, 'yaw': yaw}
        }

        return pose

    def add_pose_measurement(self, pose):
        """Add pose measurement to history for temporal smoothing"""
        if pose is not None:
            self.pose_history.append(pose)
            self.confidence_history.append(pose['confidence'])

    def get_smoothed_pose(self):
        """Get temporally smoothed pose estimate"""
        if not self.pose_history:
            return None

        if len(self.pose_history) == 1:
            return self.pose_history[0]

        # Weighted average based on confidence
        weights = np.array(list(self.confidence_history))
        weights = weights / np.sum(weights) if np.sum(weights) > 0 else np.ones_like(weights) / len(weights)

        # Smooth each angle component
        rolls = [pose['roll'] for pose in self.pose_history]
        pitches = [pose['pitch'] for pose in self.pose_history]
        yaws = [pose['yaw'] for pose in self.pose_history]

        smoothed_roll = np.average(rolls, weights=weights)
        smoothed_pitch = np.average(pitches, weights=weights)
        smoothed_yaw = np.average(yaws, weights=weights)

        # Average confidence
        avg_confidence = np.mean(list(self.confidence_history))

        # Get most recent vanishing point
        recent_vp = self.pose_history[-1]['vanishing_point']

        return {
            'vanishing_point': recent_vp,
            'roll': smoothed_roll,
            'pitch': smoothed_pitch,
            'yaw': smoothed_yaw,
            'confidence': avg_confidence,
            'smoothed': True
        }

def create_visualization_plots(pose_data, output_dir):
    """Create comprehensive visualization plots"""
    if not pose_data:
        return

    timestamps = [d['timestamp'] for d in pose_data]
    rolls = [d['roll'] for d in pose_data]
    pitches = [d['pitch'] for d in pose_data]
    yaws = [d['yaw'] for d in pose_data]
    confidences = [d['confidence'] for d in pose_data]

    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Camera Pose Estimation Results', fontsize=16)

    # Plot pose angles over time
    axes[0, 0].plot(timestamps, rolls, 'r-', label='Roll', alpha=0.7)
    axes[0, 0].plot(timestamps, pitches, 'g-', label='Pitch', alpha=0.7)
    axes[0, 0].plot(timestamps, yaws, 'b-', label='Yaw', alpha=0.7)
    axes[0, 0].set_xlabel('Time (s)')
    axes[0, 0].set_ylabel('Angle (radians)')
    axes[0, 0].set_title('Pose Angles Over Time')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # Plot confidence over time
    axes[0, 1].plot(timestamps, confidences, 'purple', alpha=0.7)
    axes[0, 1].set_xlabel('Time (s)')
    axes[0, 1].set_ylabel('Confidence')
    axes[0, 1].set_title('Detection Confidence Over Time')
    axes[0, 1].grid(True, alpha=0.3)

    # Plot pose magnitude
    pose_magnitude = [np.sqrt(r**2 + p**2 + y**2) for r, p, y in zip(rolls, pitches, yaws)]
    axes[1, 0].plot(timestamps, pose_magnitude, 'orange', alpha=0.7)
    axes[1, 0].set_xlabel('Time (s)')
    axes[1, 0].set_ylabel('Pose Magnitude (radians)')
    axes[1, 0].set_title('Overall Pose Change Magnitude')
    axes[1, 0].grid(True, alpha=0.3)

    # Histogram of pose magnitudes
    axes[1, 1].hist(pose_magnitude, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].set_xlabel('Pose Magnitude (radians)')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Distribution of Pose Magnitudes')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'pose_analysis.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Enhanced main processing function with robust algorithms"""
    json_path = "data/R9_Aw_CamS.json"
    video_path = "data/R9_Aw_CamS.mp4"

    # Load camera parameters
    K, initial_rotation, initial_translation = load_camera_parameters(json_path)
    logging.info("Camera intrinsic matrix:")
    logging.info(f"\n{K}")
    logging.info(f"Initial rotation quaternion: w={initial_rotation['w']}, x={initial_rotation['x']}, y={initial_rotation['y']}, z={initial_rotation['z']}")
    logging.info(f"Initial translation: x={initial_translation['x']}, y={initial_translation['y']}, z={initial_translation['z']}")

    # Open video file
    cap = cv2.VideoCapture(video_path)

    if not cap.isOpened():
        logging.error("Error: Cannot open video file")
        return

    # Get video information
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    logging.info(f"Video info - FPS: {fps}, Frames: {frame_count}, Size: {width}x{height}")

    # Create output directory
    output_dir = "output_robust"
    os.makedirs(output_dir, exist_ok=True)

    # Initialize robust detectors
    vp_detector = RobustVanishingPointDetector(width, height)
    pose_estimator = RobustPoseEstimator(K, initial_rotation)
    
    # Open CSV file for results
    csv_path = os.path.join(output_dir, "camera_pose_results_robust.csv")
    pose_data_for_plots = []

    with open(csv_path, 'w', newline='') as csvfile:
        fieldnames = ['frame_number', 'timestamp', 'vanishing_point_x', 'vanishing_point_y',
                     'roll', 'pitch', 'yaw', 'confidence', 'smoothed_roll', 'smoothed_pitch',
                     'smoothed_yaw', 'smoothed_confidence']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        frame_num = 0
        successful_detections = 0

        while True:
            ret, frame = cap.read()

            if not ret:
                break

            timestamp = frame_num / fps

            # Detect vanishing point using robust method
            vanishing_point, edge_image, confidence, detected_lines = vp_detector.detect_vanishing_point(frame)

            # Calculate camera pose
            pose_result = pose_estimator.vanishing_point_to_pose(vanishing_point, confidence)

            # Add to pose estimator for temporal smoothing
            pose_estimator.add_pose_measurement(pose_result)
            smoothed_pose = pose_estimator.get_smoothed_pose()

            # Prepare data for CSV
            if pose_result:
                successful_detections += 1
                csv_data = {
                    'frame_number': frame_num,
                    'timestamp': timestamp,
                    'vanishing_point_x': pose_result['vanishing_point'][0],
                    'vanishing_point_y': pose_result['vanishing_point'][1],
                    'roll': pose_result['roll'],
                    'pitch': pose_result['pitch'],
                    'yaw': pose_result['yaw'],
                    'confidence': pose_result['confidence']
                }

                # Add data for plotting
                pose_data_for_plots.append({
                    'frame': frame_num,
                    'timestamp': timestamp,
                    'roll': pose_result['roll'],
                    'pitch': pose_result['pitch'],
                    'yaw': pose_result['yaw'],
                    'confidence': pose_result['confidence']
                })
            else:
                csv_data = {
                    'frame_number': frame_num,
                    'timestamp': timestamp,
                    'vanishing_point_x': 0,
                    'vanishing_point_y': 0,
                    'roll': 0.0,
                    'pitch': 0.0,
                    'yaw': 0.0,
                    'confidence': 0.0
                }

            # Add smoothed results if available
            if smoothed_pose:
                csv_data.update({
                    'smoothed_roll': smoothed_pose['roll'],
                    'smoothed_pitch': smoothed_pose['pitch'],
                    'smoothed_yaw': smoothed_pose['yaw'],
                    'smoothed_confidence': smoothed_pose['confidence']
                })
            else:
                csv_data.update({
                    'smoothed_roll': csv_data['roll'],
                    'smoothed_pitch': csv_data['pitch'],
                    'smoothed_yaw': csv_data['yaw'],
                    'smoothed_confidence': csv_data['confidence']
                })

            writer.writerow(csv_data)

            # Save enhanced visualization results (every 10th frame to reduce storage)
            if vanishing_point and frame_num % 10 == 0:
                marked_frame = frame.copy()

                # Draw detected lines with quality-based coloring
                for i, line in enumerate(detected_lines[:10]):  # Show top 10 lines
                    endpoints = line['endpoints']
                    quality = line['quality']

                    # Color based on quality (green = high, red = low)
                    color_intensity = int(255 * min(quality * 2, 1.0))
                    color = (0, color_intensity, 255 - color_intensity)

                    cv2.line(marked_frame, endpoints[0], endpoints[1], color, 2)

                # Draw vanishing point with confidence-based size
                vp_size = max(3, int(confidence * 10))
                cv2.circle(marked_frame, (int(vanishing_point[0]), int(vanishing_point[1])),
                          vp_size, (0, 0, 255), -1)

                # Add text information
                info_text = f"Frame: {frame_num}, Conf: {confidence:.3f}"
                cv2.putText(marked_frame, info_text, (10, 30),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                pose_text = f"R:{pose_result['roll']:.3f} P:{pose_result['pitch']:.3f} Y:{pose_result['yaw']:.3f}"
                cv2.putText(marked_frame, pose_text, (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

                # Save marked frame
                marked_path = os.path.join(output_dir, f"frame_{frame_num:04d}_robust.png")
                cv2.imwrite(marked_path, marked_frame)

            frame_num += 1

            if frame_num % 50 == 0:
                logging.info(f"Processed {frame_num} frames")

    cap.release()

    # Generate comprehensive analysis plots
    logging.info("Generating analysis plots...")
    create_visualization_plots(pose_data_for_plots, output_dir)

    # Print summary statistics
    detection_rate = successful_detections / frame_num if frame_num > 0 else 0
    logging.info(f"Processing complete!")
    logging.info(f"Results saved to: {csv_path}")
    logging.info(f"Detection rate: {detection_rate:.2%} ({successful_detections}/{frame_num} frames)")

    if pose_data_for_plots:
        avg_confidence = np.mean([d['confidence'] for d in pose_data_for_plots])
        pose_magnitudes = [np.sqrt(d['roll']**2 + d['pitch']**2 + d['yaw']**2) for d in pose_data_for_plots]
        avg_pose_magnitude = np.mean(pose_magnitudes)
        max_pose_magnitude = np.max(pose_magnitudes)

        logging.info(f"Average confidence: {avg_confidence:.3f}")
        logging.info(f"Average pose magnitude: {avg_pose_magnitude:.4f} radians")
        logging.info(f"Maximum pose magnitude: {max_pose_magnitude:.4f} radians")
        logging.info(f"Analysis plots saved to: {os.path.join(output_dir, 'pose_analysis.png')}")

if __name__ == "__main__":
    main()
